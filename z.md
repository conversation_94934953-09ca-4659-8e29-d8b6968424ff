# 🚀 Workshop: Construindo uma API de Login do Zero

## Slide 1: Bem-vindos ao Workshop!

**Construindo uma API de Login com Node.js, Express e PostgreSQL**

🎯 **Objetivo**: Criar uma API REST completa com autenticação JWT

📋 **O que vamos construir**:
- Cadastro de usuários (signup)
- Login de usuários (signin)
- Visualização de perfil (protegido)
- Atualização de perfil (protegido)

---

## Slide 2: Preparação do Ambiente

### Passo 1: Inicializar o projeto

```bash
npm init -y
```

### Passo 2: Instalar dependências

```bash
npm install express bcryptjs jsonwebtoken express-validator cors dotenv pg
```

### Passo 3: Configurar Docker para PostgreSQL

**Arquivo: `docker-compose.yml`**
```yaml
version: '3.8'

services:
  postgres:
    image: postgres:15
    container_name: workshop_postgres
    environment:
      POSTGRES_DB: workshop_db
      POSTGRES_USER: workshop_user
      POSTGRES_PASSWORD: workshop_password
    ports:
      - "5433:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    restart: unless-stopped

volumes:
  postgres_data:
```

---

## Slide 3: Configuração do Banco de Dados

### Passo 4: Script de inicialização do banco

**Arquivo: `init.sql`**
```sql
-- Create users table
CREATE TABLE IF NOT EXISTS users (
    id SERIAL PRIMARY KEY,
    email VARCHAR(255) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create index on email for faster lookups
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);

-- Create a trigger to automatically update the updated_at column
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_users_updated_at
    BEFORE UPDATE ON users
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();
```

### Passo 5: Variáveis de ambiente

**Arquivo: `.env`**
```env
# Server Configuration
PORT=3000
NODE_ENV=development

# Database Configuration
DB_HOST=localhost
DB_PORT=5433
DB_NAME=workshop_db
DB_USER=workshop_user
DB_PASSWORD=workshop_password

# JWT Configuration
JWT_SECRET=your_super_secret_jwt_key_change_this_in_production
JWT_EXPIRES_IN=24h
```

---

## Slide 4: Conexão com o Banco

### Passo 6: Configurar conexão PostgreSQL

**Arquivo: `config/database.js`**
```javascript
const { Pool } = require('pg');
require('dotenv').config();

// Create a connection pool
const pool = new Pool({
  host: process.env.DB_HOST,
  port: process.env.DB_PORT,
  database: process.env.DB_NAME,
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  max: 20, // Maximum number of clients in the pool
  idleTimeoutMillis: 30000, // Close idle clients after 30 seconds
  connectionTimeoutMillis: 2000, // Return an error after 2 seconds if connection could not be established
});

// Test the connection
pool.on('connect', () => {
  console.log('Connected to PostgreSQL database');
});

pool.on('error', (err) => {
  console.error('Unexpected error on idle client', err);
  process.exit(-1);
});

module.exports = pool;
```

---

## Slide 5: Modelo de Usuário

### Passo 7: Criar o modelo User

**Arquivo: `models/User.js`** (Parte 1)
```javascript
const pool = require('../config/database');
const bcrypt = require('bcryptjs');

class User {
  // Create a new user
  static async create(userData) {
    const { email, password, firstName, lastName } = userData;

    // Hash the password
    const saltRounds = 12;
    const hashedPassword = await bcrypt.hash(password, saltRounds);

    const query = `
      INSERT INTO users (email, password, first_name, last_name)
      VALUES ($1, $2, $3, $4)
      RETURNING id, email, first_name, last_name, created_at
    `;

    const values = [email, hashedPassword, firstName, lastName];

    try {
      const result = await pool.query(query, values);
      return result.rows[0];
    } catch (error) {
      throw error;
    }
  }
```

---

## Slide 6: Modelo de Usuário (Continuação)

### Passo 7: Completar o modelo User

**Arquivo: `models/User.js`** (Parte 2)
```javascript
  // Find user by email
  static async findByEmail(email) {
    const query = 'SELECT * FROM users WHERE email = $1';

    try {
      const result = await pool.query(query, [email]);
      return result.rows[0];
    } catch (error) {
      throw error;
    }
  }

  // Find user by ID
  static async findById(id) {
    const query = 'SELECT id, email, first_name, last_name, created_at, updated_at FROM users WHERE id = $1';

    try {
      const result = await pool.query(query, [id]);
      return result.rows[0];
    } catch (error) {
      throw error;
    }
  }

  // Update user profile
  static async updateProfile(id, updateData) {
    const { firstName, lastName } = updateData;

    const query = `
      UPDATE users
      SET first_name = $1, last_name = $2, updated_at = CURRENT_TIMESTAMP
      WHERE id = $3
      RETURNING id, email, first_name, last_name, updated_at
    `;

    const values = [firstName, lastName, id];

    try {
      const result = await pool.query(query, values);
      return result.rows[0];
    } catch (error) {
      throw error;
    }
  }

  // Verify password
  static async verifyPassword(plainPassword, hashedPassword) {
    return await bcrypt.compare(plainPassword, hashedPassword);
  }
}

module.exports = User;
```

---

## Slide 7: Middleware de Autenticação

### Passo 8: Criar middleware JWT

**Arquivo: `middleware/auth.js`**
```javascript
const jwt = require('jsonwebtoken');
const User = require('../models/User');

const authenticateToken = async (req, res, next) => {
  // Get token from header
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

  if (!token) {
    return res.status(401).json({
      success: false,
      message: 'Access token is required'
    });
  }

  try {
    // Verify token
    const decoded = jwt.verify(token, process.env.JWT_SECRET);

    // Get user from database
    const user = await User.findById(decoded.userId);

    if (!user) {
      return res.status(401).json({
        success: false,
        message: 'Invalid token - user not found'
      });
    }

    // Add user to request object
    req.user = user;
    next();
  } catch (error) {
    if (error.name === 'JsonWebTokenError') {
      return res.status(401).json({
        success: false,
        message: 'Invalid token'
      });
    } else if (error.name === 'TokenExpiredError') {
      return res.status(401).json({
        success: false,
        message: 'Token expired'
      });
    } else {
      console.error('Auth middleware error:', error);
      return res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }
};

module.exports = authenticateToken;
```

---

## Slide 8: Rotas de Autenticação (Parte 1)

### Passo 9: Criar rotas de signup e signin

**Arquivo: `routes/auth.js`** (Parte 1)
```javascript
const express = require('express');
const jwt = require('jsonwebtoken');
const { body, validationResult } = require('express-validator');
const User = require('../models/User');

const router = express.Router();

// Validation rules for signup
const signupValidation = [
  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Please provide a valid email'),
  body('password')
    .isLength({ min: 6 })
    .withMessage('Password must be at least 6 characters long'),
  body('firstName')
    .trim()
    .isLength({ min: 1 })
    .withMessage('First name is required'),
  body('lastName')
    .trim()
    .isLength({ min: 1 })
    .withMessage('Last name is required')
];

// Validation rules for signin
const signinValidation = [
  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Please provide a valid email'),
  body('password')
    .notEmpty()
    .withMessage('Password is required')
];

// Helper function to generate JWT token
const generateToken = (userId) => {
  return jwt.sign(
    { userId },
    process.env.JWT_SECRET,
    { expiresIn: process.env.JWT_EXPIRES_IN }
  );
};
```

---

## Slide 9: Rotas de Autenticação (Parte 2)

### Passo 9: Implementar endpoint de signup

**Arquivo: `routes/auth.js`** (Parte 2)
```javascript
// POST /api/auth/signup - User registration
router.post('/signup', signupValidation, async (req, res) => {
  try {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { email, password, firstName, lastName } = req.body;

    // Check if user already exists
    const existingUser = await User.findByEmail(email);
    if (existingUser) {
      return res.status(409).json({
        success: false,
        message: 'User with this email already exists'
      });
    }

    // Create new user
    const newUser = await User.create({
      email,
      password,
      firstName,
      lastName
    });

    // Generate JWT token
    const token = generateToken(newUser.id);

    res.status(201).json({
      success: true,
      message: 'User created successfully',
      data: {
        user: {
          id: newUser.id,
          email: newUser.email,
          firstName: newUser.first_name,
          lastName: newUser.last_name,
          createdAt: newUser.created_at
        },
        token
      }
    });

  } catch (error) {
    console.error('Signup error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});
```

---

## Slide 10: Rotas de Autenticação (Parte 3)

### Passo 9: Implementar endpoint de signin

**Arquivo: `routes/auth.js`** (Parte 3)
```javascript
// POST /api/auth/signin - User login
router.post('/signin', signinValidation, async (req, res) => {
  try {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { email, password } = req.body;

    // Find user by email
    const user = await User.findByEmail(email);
    if (!user) {
      return res.status(401).json({
        success: false,
        message: 'Invalid email or password'
      });
    }

    // Verify password
    const isPasswordValid = await User.verifyPassword(password, user.password);
    if (!isPasswordValid) {
      return res.status(401).json({
        success: false,
        message: 'Invalid email or password'
      });
    }

    // Generate JWT token
    const token = generateToken(user.id);

    res.json({
      success: true,
      message: 'Login successful',
      data: {
        user: {
          id: user.id,
          email: user.email,
          firstName: user.first_name,
          lastName: user.last_name
        },
        token
      }
    });

  } catch (error) {
    console.error('Signin error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

module.exports = router;
```

---

## Slide 11: Rotas de Perfil

### Passo 10: Criar rotas protegidas de perfil

**Arquivo: `routes/profile.js`**
```javascript
const express = require('express');
const { body, validationResult } = require('express-validator');
const User = require('../models/User');
const authenticateToken = require('../middleware/auth');

const router = express.Router();

// Validation rules for profile update
const updateProfileValidation = [
  body('firstName')
    .trim()
    .isLength({ min: 1 })
    .withMessage('First name is required'),
  body('lastName')
    .trim()
    .isLength({ min: 1 })
    .withMessage('Last name is required')
];

// GET /api/profile - View user profile
router.get('/', authenticateToken, async (req, res) => {
  try {
    // User is already available from the auth middleware
    const user = req.user;

    res.json({
      success: true,
      message: 'Profile retrieved successfully',
      data: {
        user: {
          id: user.id,
          email: user.email,
          firstName: user.first_name,
          lastName: user.last_name,
          createdAt: user.created_at,
          updatedAt: user.updated_at
        }
      }
    });

  } catch (error) {
    console.error('Get profile error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});
```
---

## Slide 12: Rotas de Perfil (Continuação)

### Passo 10: Implementar atualização de perfil

**Arquivo: `routes/profile.js`** (Continuação)
```javascript
// PUT /api/profile - Update user profile
router.put('/', authenticateToken, updateProfileValidation, async (req, res) => {
  try {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { firstName, lastName } = req.body;
    const userId = req.user.id;

    // Update user profile
    const updatedUser = await User.updateProfile(userId, {
      firstName,
      lastName
    });

    if (!updatedUser) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    res.json({
      success: true,
      message: 'Profile updated successfully',
      data: {
        user: {
          id: updatedUser.id,
          email: updatedUser.email,
          firstName: updatedUser.first_name,
          lastName: updatedUser.last_name,
          updatedAt: updatedUser.updated_at
        }
      }
    });

  } catch (error) {
    console.error('Update profile error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

module.exports = router;
```

---

## Slide 13: Servidor Principal

### Passo 11: Criar o servidor principal

**Arquivo: `server.js`**
```javascript
const express = require('express');
const cors = require('cors');
require('dotenv').config();

// Import routes
const authRoutes = require('./routes/auth');
const profileRoutes = require('./routes/profile');

// Import database connection to test it
const pool = require('./config/database');

const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Request logging middleware
app.use((req, res, next) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.path}`);
  next();
});

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    success: true,
    message: 'Server is running',
    timestamp: new Date().toISOString()
  });
});

// API Routes
app.use('/api/auth', authRoutes);
app.use('/api/profile', profileRoutes);

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    message: 'Route not found'
  });
});

// Global error handler
app.use((error, req, res, next) => {
  console.error('Global error handler:', error);
  res.status(500).json({
    success: false,
    message: 'Internal server error'
  });
});
```

---

## Slide 14: Servidor Principal (Continuação)

### Passo 11: Finalizar servidor e inicialização

**Arquivo: `server.js`** (Continuação)
```javascript
// Test database connection and start server
const startServer = async () => {
  try {
    // Test database connection
    await pool.query('SELECT NOW()');
    console.log('Database connection successful');

    // Start server
    app.listen(PORT, () => {
      console.log(`Server is running on port ${PORT}`);
      console.log(`Health check: http://localhost:${PORT}/health`);
      console.log('\nAvailable endpoints:');
      console.log('POST /api/auth/signup - User registration');
      console.log('POST /api/auth/signin - User login');
      console.log('GET  /api/profile - View profile (requires auth)');
      console.log('PUT  /api/profile - Update profile (requires auth)');
    });
  } catch (error) {
    console.error('Failed to start server:', error);
    process.exit(1);
  }
};

startServer();
```

### Passo 12: Atualizar package.json

**Arquivo: `package.json`** (adicionar scripts)
```json
{
  "scripts": {
    "start": "node server.js",
    "dev": "nodemon server.js",
    "db:up": "docker-compose up -d",
    "db:down": "docker-compose down",
    "db:logs": "docker-compose logs postgres"
  }
}
```

### Passo 13: Executar o projeto

```bash
# Iniciar o banco de dados
npm run db:up

# Iniciar o servidor
npm start
```

---

## Slide 15: Testando a API

### Teste 1: Cadastro de usuário
```bash
curl -X POST http://localhost:3000/api/auth/signup \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123",
    "firstName": "Test",
    "lastName": "User"
  }'
```

### Teste 2: Login
```bash
curl -X POST http://localhost:3000/api/auth/signin \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123"
  }'
```

### Teste 3: Ver perfil (usar token do login)
```bash
curl -X GET http://localhost:3000/api/profile \
  -H "Authorization: Bearer SEU_TOKEN_AQUI"
```

### Teste 4: Atualizar perfil
```bash
curl -X PUT http://localhost:3000/api/profile \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer SEU_TOKEN_AQUI" \
  -d '{
    "firstName": "Novo",
    "lastName": "Nome"
  }'
```

---

## Slide 16: Conceitos Aprendidos

✅ **REST API**: Endpoints seguindo princípios REST
✅ **Autenticação JWT**: Tokens seguros para autenticação
✅ **Hash de senhas**: Segurança com bcrypt
✅ **Validação de dados**: express-validator
✅ **Banco PostgreSQL**: Operações CRUD
✅ **Middleware**: Autenticação e logging
✅ **Tratamento de erros**: Respostas consistentes
✅ **Docker**: Containerização do banco

---

## Slide 17: Arquitetura Final

```
📁 projeto/
├── 📄 server.js              # Servidor principal
├── 📄 docker-compose.yml     # PostgreSQL
├── 📄 init.sql               # Schema do banco
├── 📄 .env                   # Variáveis de ambiente
├── 📁 config/
│   └── 📄 database.js        # Conexão PostgreSQL
├── 📁 models/
│   └── 📄 User.js            # Modelo de usuário
├── 📁 middleware/
│   └── 📄 auth.js            # Autenticação JWT
└── 📁 routes/
    ├── 📄 auth.js            # Signup/Signin
    └── 📄 profile.js         # Perfil protegido
```

---

## Slide 18: Próximos Passos

🚀 **Melhorias possíveis**:
- ✉️ Verificação de email
- 🔑 Reset de senha
- 🔄 Refresh tokens
- ⚡ Rate limiting
- 📊 Logs estruturados
- 🧪 Testes automatizados
- 🌐 Deploy em produção
- 🔒 HTTPS/SSL
- 📱 API versioning

---

## Slide 19: Recursos Adicionais

📚 **Documentação**:
- [Express.js](https://expressjs.com/)
- [JWT.io](https://jwt.io/)
- [PostgreSQL](https://www.postgresql.org/docs/)
- [bcrypt](https://github.com/kelektiv/node.bcrypt.js)

🛠️ **Ferramentas úteis**:
- [Postman](https://www.postman.com/) - Teste de APIs
- [pgAdmin](https://www.pgadmin.org/) - Interface PostgreSQL
- [Nodemon](https://nodemon.io/) - Auto-reload
- [Docker Desktop](https://www.docker.com/products/docker-desktop/)

---

## Slide 20: Perguntas e Respostas

💬 **Dúvidas?**

📧 **Contato**: [seu-email]
🔗 **GitHub**: [seu-github]
📱 **LinkedIn**: [seu-linkedin]

**Obrigado pela participação!** 🎉

### 🎯 Resumo do que construímos:
- ✅ API REST completa com 4 endpoints
- ✅ Autenticação JWT segura
- ✅ Banco PostgreSQL com Docker
- ✅ Validação de dados
- ✅ Tratamento de erros
- ✅ Código organizado e profissional