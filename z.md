## ✨ Apresentação: Como Criar um Sistema de Login com Node.js e Express

### Slide 1: T<PERSON><PERSON>lo

**Como Criar um Sistema de Login com Node.js e Express**

* Seu nome
* Nome da faculdade/curso
* Data da apresentação

### Slide 2: Objetivo da Apresentação

* Explicar como funciona um sistema de autenticação com Node.js
* Mostrar o uso de JWT para autenticação
* Apresentar um projeto funcional com cadastro, login e perfil protegido

### Slide 3: Estrutura do Projeto

* `app.js` (ponto de entrada)
* `routes/` (rotas `auth` e `profile`)
* `models/User.js` (acesso ao banco)
* `middleware/auth.js` (validação do token JWT)
* `config/database.js` (conexão com o banco)

### Slide 4: Dependências Utilizadas

* `express`
* `cors`
* `dotenv`
* `jsonwebtoken`
* `express-validator`
* `pg` (ou `mysql`, dependendo do banco)

### Slide 5: Inicializando o Servidor

* Middleware (`express.json`, `cors`)
* Middleware de log
* Rota `/health`
* Teste da conexão com o banco

### Slide 6: Cadastro de Usuário (Signup)

* Endpoint: `POST /api/auth/signup`
* Validação de dados
* Verificação de e-mail existente
* Criação do usuário + JWT

### Slide 7: Login de Usuário (Signin)

* Endpoint: `POST /api/auth/signin`
* Validação de dados
* Verificação de senha e e-mail
* Geração de token JWT

### Slide 8: Autenticação com JWT

* Middleware `authenticateToken`
* Validação do token
* Disponibiliza `req.user`

### Slide 9: Acesso ao Perfil

* Rota protegida: `GET /api/profile`
* Retorna dados do usuário autenticado

### Slide 10: Atualização de Perfil

* Rota: `PUT /api/profile`
* Valida novos dados (nome, sobrenome)
* Atualiza no banco

### Slide 11: Tratamento de Erros

* `express-validator`
* Retornos padronizados
* Middleware global para erros inesperados

### Slide 12: Testando com Postman

* Teste de cadastro
* Teste de login
* Teste de acesso ao perfil com token

### Slide 13: Considerações Finais

* Sistema simples e funcional
* Próximos passos: hash de senha, confirmação de e-mail, etc

### Slide 14: Perguntas

* Espaço para dúvidas

---

## 🖍️ Paleta de Cores baseada no Node.js

| Element          | Color Name             | Hex           | Notes                                        |
| ---------------- | ---------------------- | ------------- | -------------------------------------------- |
| Background       | Off-white / light gray | #F6F6F6       | Clean and neutral                            |
| Title Text       | Node.js Green          | #339933       | Strong branding                              |
| Body Text        | Dark Gray              | #333333       | Better contrast than pure black              |
| Accent Shapes    | Node.js Green          | #339933       | Used in headers, bullets, icons              |
| Highlight Box    | Soft Green             | #E6F4EA       | Light background box for callouts            |
| Links or Buttons | Bright Blue            | #007ACC       | Harmonizes well and stands out               |
| Code Snippets    | Background: #1E1E1E    | Text: #C5C8C6 | Dark background for contrast with light text |
